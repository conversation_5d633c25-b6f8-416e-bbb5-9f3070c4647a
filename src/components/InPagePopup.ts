import { createShadowRootUi } from 'wxt/client';

interface FormField {
  element: HTMLElement;
  type: string;
  id: string;
  name: string;
  placeholder?: string;
  label?: string;
}

interface LoginStatus {
  isLoggedIn: boolean;
  skipLogin: boolean;
}

interface UserInfo {
  user?: {
    name?: string;
    picture_url?: string;
  };
}

interface PopupConfig {
  mode: string;
  language: string;
  projects: Array<{ id: string; name: string; }>;
  hasFormFields: boolean;
}

// 新增：弹窗持久化状态接口
interface PopupPersistentState {
  isActive: boolean; // 是否有活跃的生成任务
  description: string; // 输入内容
  language: string; // 选择的语言
  project: string; // 选择的项目
  buttonState: 'generate' | 'loading' | 'thinking' | 'success'; // 按钮状态
  mode: string; // 当前模式
  timestamp: number; // 状态保存时间
  url: string; // 页面URL，确保状态只在同一页面恢复
  reasoningContent?: string; // 推理内容（如果有的话）
}



export class InPagePopup {
  private ui: Awaited<ReturnType<typeof createShadowRootUi>> | null = null;
  private ctx: any; // ContentScriptContext
  private isDragging = false;
  private dragStartX = 0;
  private dragStartY = 0;
  private popupStartX = 0;
  private popupStartY = 0;
  private longPressTimer: number | null = null;
  private isLongPressTriggered = false;
  private animationFrame: number | null = null;
  private reasoningBubble: HTMLElement | null = null;
  private buttonResetTimer: number | null = null; // 新增：按钮重置定时器
  
  // 新增：持久化状态管理
  private static readonly STORAGE_KEY = 'fillify_popup_state';
  private persistentState: PopupPersistentState | null = null;

  constructor(ctx: any) {
    this.ctx = ctx;
    this.setupStreamingListeners();
  }

  /**
   * 设置流式处理监听器
   */
  private setupStreamingListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'streamingUpdate') {
        this.handleStreamingUpdate(message);
      } else if (message.type === 'streamingComplete') {
        this.handleStreamingComplete(message);
      }
    });
  }

  /**
   * 处理流式更新 - 增量内容更新
   */
  public handleStreamingUpdate(message: any) {
    console.log('[InPagePopup] Streaming update received:', {
      contentLength: message.content?.length || 0,
      reasoningLength: message.reasoning?.length || 0,
      isComplete: message.isComplete
    });

    // 实时更新推理气泡内容（如果有推理内容）
    if (message.reasoning) {
      this.updateReasoningBubble(message.reasoning, false);
    }

    // 更新生成状态显示（显示当前内容长度）
    if (message.content) {
      this.updateGeneratingStatus(`Generated ${message.content.length} characters...`, false);
    }
  }

  /**
   * 处理流式完成
   */
  public async handleStreamingComplete(message: any) {
    console.log('[InPagePopup] Streaming complete:', message);

    if (!this.ui) return;
    const container = this.ui.uiContainer;

    try {
      // 最终更新推理气泡内容
      if (message.reasoning) {
        this.updateReasoningBubble(message.reasoning, true); // true 表示完成状态
      }

      // 填充表单
      if (message.data) {
        await this.fillForm(message.data);

        // 使用新的按钮状态管理方法
        this.setButtonSuccessState(container, true); // 启用自动重置

        // 显示彩带效果
        this.createConfetti(container);

        // 延迟隐藏推理气泡，但不自动关闭弹窗
        setTimeout(() => {
          this.hideReasoningBubble();
          // 移除自动关闭弹窗的逻辑，让用户手动关闭
        }, 3000); // 给用户更多时间查看推理内容
      }
    } catch (error) {
      console.error('[InPagePopup] Error in streaming complete:', error);
      this.showStatusMessage(error instanceof Error ? error.message : 'An error occurred', 'error');

      // 恢复按钮状态
      this.updateGeneratingStatus('', true);
    } finally {
      // 清除生成效果
      this.removeGeneratingEffect();
    }
  }

  /**
   * 创建推理气泡
   */
  private createReasoningBubble(): HTMLElement {
    const card = document.createElement('div');
    card.className = 'fillify-reasoning-card';
    card.style.cssText = `
      position: fixed;
      z-index: 2147483646;
      width: 380px;
      height: 150px;
      border-radius: 14px;
      overflow: hidden;
      opacity: 0;
      transform: scale(0.8) translateY(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: auto;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    `;

    // 添加blob动画层 (现在在card层级)
    const blob = document.createElement('div');
    blob.className = 'fillify-reasoning-blob';
    blob.style.cssText = `
      position: absolute;
      z-index: 1;
      width: 150px;
      height: 150px;
      border-radius: 50%;
      background: #2962FF;
      opacity: 1;
      filter: blur(12px);
      /* Use motion path for smooth, transform-only animation */
      offset-path: inset(5px round 10px);
      offset-distance: 0%;
      offset-rotate: 0deg;
      will-change: transform, offset-distance;
      contain: layout paint;
      transform: translateZ(0);
      animation: fillify-blob-bounce 3s linear infinite;
    `;

    // 添加背景层
    const bg = document.createElement('div');
    bg.className = 'fillify-reasoning-bg';
    bg.style.cssText = `
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      z-index: 2;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(24px);
      border-radius: 10px;
      overflow: hidden;
      outline: 2px solid white;
      padding: 16px 20px;
    `;

    // Add close button (only closes the reasoning bubble)
    const closeBtn = document.createElement('button');
    closeBtn.className = 'fillify-reasoning-close';
    closeBtn.setAttribute('aria-label', 'Close');
    closeBtn.innerHTML = '&times;';
    closeBtn.style.cssText = `
      position: absolute;
      top: 8px;
      right: 8px;
      width: 24px;
      height: 24px;
      border: none;
      background: transparent;
      color: #999;
      font-size: 20px;
      line-height: 20px;
      cursor: pointer;
      z-index: 3;
    `;
    closeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.hideReasoningBubble();
    });
    bg.appendChild(closeBtn);

    // 添加内容区域
    const content = document.createElement('div');
    content.className = 'fillify-reasoning-content';
    content.style.cssText = `
      font-size: 13px;
      line-height: 1.5;
      color: #666;
      height: 100px;
      overflow-y: auto;
      margin-top: 8px;
      padding-right: 8px;
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    `;

    // 添加自定义滚动条样式
    const scrollbarStyle = document.createElement('style');
    scrollbarStyle.textContent = `
      .fillify-reasoning-content::-webkit-scrollbar {
        width: 6px;
      }
      .fillify-reasoning-content::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
      }
      .fillify-reasoning-content::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }
      .fillify-reasoning-content::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
      }

      .fillify-thinking-shine {
        position: relative;
        display: inline-block;
        /* Text color via gradient fill, clipped to glyphs */
        background: linear-gradient(
          90deg,
          #666 0%,
          #666 47%,
          #e6e6e6 50%,
          #666 53%,
          #666 100%
        );
        background-size: 300% 100%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        color: transparent;
        animation: shine 3s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        will-change: background-position;
      }
      @keyframes shine {
        0% { background-position: -150% 0; }
        100% { background-position: 150% 0; }
      }

      /* Default: smooth motion-path animation (transform-only) */
      @keyframes fillify-blob-bounce {
        from { offset-distance: 0%; }
        to   { offset-distance: 100%; }
      }

      /* Fallback for browsers without motion-path support */
      @supports not (offset-path: inset(1px)) {
        @keyframes fillify-blob-bounce {
          0%, 100% { transform: translate(-50%, -50%); top: 0%;   left: 0%; }
          25%      { transform: translate(-50%, -50%); top: 0%;   left: 100%; }
          50%      { transform: translate(-50%, -50%); top: 100%; left: 100%; }
          75%      { transform: translate(-50%, -50%); top: 100%; left: 0%; }
        }
      }

      /* Respect reduced motion preferences */
      @media (prefers-reduced-motion: reduce) {
        .fillify-reasoning-blob { animation-duration: 12s; }
      }
    `;

    bg.appendChild(content);
    card.appendChild(blob);  // blob现在在card层级
    card.appendChild(bg);
    card.appendChild(scrollbarStyle);

    return card;
  }

  /**
   * 更新推理气泡内容
   */
  private updateReasoningBubble(reasoning: string, isComplete: boolean = false) {
    if (!this.ui) return;

    // 如果气泡不存在，创建它
    if (!this.reasoningBubble) {
      this.reasoningBubble = this.createReasoningBubble();
      document.body.appendChild(this.reasoningBubble);

      // 计算位置
      this.positionReasoningBubble();

      // 显示动画
      requestAnimationFrame(() => {
        if (this.reasoningBubble) {
          this.reasoningBubble.style.opacity = '1';
          this.reasoningBubble.style.transform = 'scale(1) translateY(0)';
        }
      });

      // 关键修复：首次创建推理气泡时，显示表单字段的蓝色呼吸效果
      this.showGeneratingEffect();
      
      // 保存thinking状态
      if (this.ui) {
        this.savePersistentState('thinking');
      }
    } else {
      // 如果气泡已存在，重新计算位置（防止 popup 被拖拽后位置变化）
      this.positionReasoningBubble();
    }

    // 更新内容
    const content = this.reasoningBubble.querySelector('.fillify-reasoning-content') as HTMLElement;
    if (content) {
      content.textContent = reasoning;

      // 使用 requestAnimationFrame 确保滚动在内容更新后执行
      requestAnimationFrame(() => {
        if (content) {
          content.scrollTop = content.scrollHeight; // 自动滚动到底部
        }
      });
    }

    // 根据 reasoning bubble 可见性同步按钮为 thinking，但保持表单UI可用性
    if (this.ui) {
      const container = this.ui.uiContainer;
      const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
      const btnText = generateBtn?.querySelector('.fillify-button-text') as HTMLElement;
      const sparkleIcon = generateBtn?.querySelector('.fillify-sparkle-icon') as HTMLElement;
      const loadingAnimation = generateBtn?.querySelector('.fillify-animation') as HTMLElement;
      
      // 获取表单元素，确保在thinking状态下不禁用表单
      const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
      const projectSelect = container.querySelector('#fillify-project-select') as HTMLSelectElement;
      const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;

      if (generateBtn && btnText) {
        if (!isComplete) {
          generateBtn.disabled = true;
          generateBtn.classList.remove('loading', 'success');
          generateBtn.classList.add('thinking');
          btnText.textContent = 'Thinking...';
          if (sparkleIcon) sparkleIcon.classList.add('hidden');
          if (loadingAnimation) loadingAnimation.style.display = 'block';
          
          // 关键修复：在thinking状态下保持表单UI可用
          if (descriptionTextarea && descriptionTextarea.disabled) {
            descriptionTextarea.disabled = false;
          }
          if (projectSelect && projectSelect.disabled) {
            projectSelect.disabled = false;
          }
          if (languageSelect && languageSelect.disabled) {
            languageSelect.disabled = false;
          }
        } else {
          // 完成时退出 thinking，后续 success/恢复逻辑会接管
          generateBtn.classList.remove('thinking');
        }
      }
    }
  }

  /**
   * 计算推理气泡的位置
   */
  private positionReasoningBubble() {
    if (!this.ui || !this.reasoningBubble) return;

    const popupElement = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
    if (!popupElement) return;

    const popupRect = popupElement.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // 固定气泡高度和间距
    const bubbleHeight = 150; // 固定高度
    const spacing = 15; // 与 popup 的固定间距

    // 计算是否有足够空间在上方显示
    const spaceAbove = popupRect.top;
    const spaceBelow = viewportHeight - popupRect.bottom;

    let top: number;
    let arrowPosition: 'top' | 'bottom';

    // 箭头高度（箭头会伸出气泡边界）
    const arrowHeight = 8;

    // 优先在上方显示
    if (spaceAbove >= bubbleHeight + spacing + arrowHeight + 20) {
      // 在上方显示，气泡底部距离 popup 顶部 spacing 距离
      top = popupRect.top - bubbleHeight - spacing;
      arrowPosition = 'bottom';
    } else if (spaceBelow >= bubbleHeight + spacing + arrowHeight + 20) {
      // 在下方显示，气泡顶部距离 popup 底部 spacing 距离
      top = popupRect.bottom + spacing;
      arrowPosition = 'top';
    } else {
      // 空间不足，选择空间较大的一侧
      if (spaceAbove > spaceBelow) {
        top = Math.max(20, popupRect.top - bubbleHeight - spacing);
        arrowPosition = 'bottom';
      } else {
        top = popupRect.bottom + spacing;
        arrowPosition = 'top';
      }
    }

    // 宽度与 popup 一致，左对齐
    const bubbleWidth = popupRect.width;
    let left = popupRect.left;

    // 确保不超出视口边界
    if (left < 20) left = 20;
    if (left + bubbleWidth > viewportWidth - 20) {
      left = Math.max(20, viewportWidth - bubbleWidth - 20);
    }

    // 应用位置和尺寸
    this.reasoningBubble.style.top = `${top}px`;
    this.reasoningBubble.style.left = `${left}px`;
    this.reasoningBubble.style.width = `${bubbleWidth}px`;

  }

  /**
   * 隐藏推理气泡
   */
  private hideReasoningBubble() {
    if (this.reasoningBubble) {
      this.reasoningBubble.style.opacity = '0';
      this.reasoningBubble.style.transform = 'scale(0.8) translateY(10px)';

      setTimeout(() => {
        if (this.reasoningBubble && this.reasoningBubble.parentNode) {
          this.reasoningBubble.parentNode.removeChild(this.reasoningBubble);
          this.reasoningBubble = null;
        }
      }, 300);
    }
  }

  /**
   * 更新生成状态
   */
  private updateGeneratingStatus(delta: string, isComplete: boolean) {
    if (!this.ui) return;

    const container = this.ui.uiContainer;
    if (!container) return;

    // 查找生成按钮
    const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
    const btnText = generateBtn?.querySelector('.fillify-button-text') as HTMLElement;
    const loadingAnimation = generateBtn?.querySelector('.fillify-animation') as HTMLElement;
    const sparkleIcon = generateBtn?.querySelector('.fillify-sparkle-icon') as HTMLElement;
    
    // 获取表单元素，确保在thinking/loading状态下保持可用
    const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
    const projectSelect = container.querySelector('#fillify-project-select') as HTMLSelectElement;
    const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;

    if (!generateBtn || !btnText) return;

    if (isComplete) {
      generateBtn.disabled = false;
      generateBtn.classList.remove('loading', 'thinking');
      btnText.textContent = 'Generate';
      if (sparkleIcon) sparkleIcon.classList.remove('hidden');
      if (loadingAnimation) loadingAnimation.style.display = 'none';
    } else {
      generateBtn.disabled = true;
      generateBtn.classList.remove('success');
      if (this.reasoningBubble) {
        // Thinking 模型：bubble 存在时进入 thinking
        generateBtn.classList.remove('loading');
        generateBtn.classList.add('thinking');
        btnText.textContent = 'Thinking...';
      } else {
        // 普通生成中
        generateBtn.classList.remove('thinking');
        generateBtn.classList.add('loading');
        btnText.textContent = 'Generating...';
      }
      if (sparkleIcon) sparkleIcon.classList.add('hidden');
      if (loadingAnimation) loadingAnimation.style.display = 'block';
      
      // 关键修复：在thinking/loading状态下保持表单UI可用
      if (descriptionTextarea && descriptionTextarea.disabled) {
        descriptionTextarea.disabled = false;
      }
      if (projectSelect && projectSelect.disabled) {
        projectSelect.disabled = false;
      }
      if (languageSelect && languageSelect.disabled) {
        languageSelect.disabled = false;
      }
    }
  }

  /**
   * 设置按钮为成功状态，并安排自动恢复
   */
  private setButtonSuccessState(container: HTMLElement, autoReset: boolean = true) {
    const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
    const btnText = generateBtn?.querySelector('.fillify-button-text') as HTMLElement;
    const loadingAnimation = generateBtn?.querySelector('.fillify-animation') as HTMLElement;
    const sparkleIcon = generateBtn?.querySelector('.fillify-sparkle-icon') as HTMLElement;

    if (generateBtn && btnText) {
      generateBtn.classList.remove('loading', 'thinking');
      generateBtn.classList.add('success');
      btnText.textContent = 'Finish';
      if (loadingAnimation) loadingAnimation.style.display = 'none';
      if (sparkleIcon) sparkleIcon.classList.add('hidden');

      // 保存成功状态
      this.savePersistentState('success');

      // 清除之前的定时器
      if (this.buttonResetTimer) {
        clearTimeout(this.buttonResetTimer);
        this.buttonResetTimer = null;
      }

      // 如果启用自动重置，设置3秒后恢复按钮状态
      if (autoReset) {
        this.buttonResetTimer = window.setTimeout(() => {
          this.resetButtonToGenerateState(container);
        }, 3000); // 3秒后重置
      }
    }
  }

  /**
   * 重置按钮到初始生成状态
   */
  private resetButtonToGenerateState(container: HTMLElement) {
    const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
    const btnText = generateBtn?.querySelector('.fillify-button-text') as HTMLElement;
    const sparkleIcon = generateBtn?.querySelector('.fillify-sparkle-icon') as HTMLElement;

    if (generateBtn && btnText) {
      generateBtn.disabled = false;
      generateBtn.classList.remove('success', 'loading', 'thinking');
      btnText.textContent = 'Generate';
      if (sparkleIcon) sparkleIcon.classList.remove('hidden');
      
      // 清除定时器引用
      this.buttonResetTimer = null;
      
      // 保存重置状态（相当于清除持久化状态）
      this.clearPersistentState();
    }
  }

  /**
   * 保存弹窗状态到localStorage
   */
  private savePersistentState(buttonState: 'generate' | 'loading' | 'thinking' | 'success' = 'generate') {
    if (!this.ui) return;

    const container = this.ui.uiContainer;
    const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
    const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;
    const projectSelect = container.querySelector('#fillify-project-select') as HTMLSelectElement;

    // 获取当前模式
    const currentMode = this.getCurrentMode(container);

    const state: PopupPersistentState = {
      isActive: buttonState !== 'generate', // 只有在generate状态下才不活跃
      description: descriptionTextarea?.value || '',
      language: languageSelect?.value || 'auto',
      project: projectSelect?.value || '',
      buttonState,
      mode: currentMode,
      timestamp: Date.now(),
      url: window.location.href,
      reasoningContent: this.reasoningBubble ? 
        this.reasoningBubble.querySelector('.fillify-reasoning-content')?.textContent || '' : 
        undefined
    };

    this.persistentState = state;
    
    try {
      localStorage.setItem(InPagePopup.STORAGE_KEY, JSON.stringify(state));
      console.log('[InPagePopup] State saved:', buttonState);
    } catch (error) {
      console.warn('[InPagePopup] Failed to save state:', error);
    }
  }

  /**
   * 从localStorage恢复弹窗状态
   */
  private loadPersistentState(): PopupPersistentState | null {
    try {
      const stateStr = localStorage.getItem(InPagePopup.STORAGE_KEY);
      if (!stateStr) return null;

      const state: PopupPersistentState = JSON.parse(stateStr);
      
      // 检查状态是否过期（24小时）
      const now = Date.now();
      const oneDay = 24 * 60 * 60 * 1000;
      if (now - state.timestamp > oneDay) {
        this.clearPersistentState();
        return null;
      }

      // 检查是否是同一个页面
      if (state.url !== window.location.href) {
        return null;
      }

      this.persistentState = state;
      console.log('[InPagePopup] State loaded:', state.buttonState);
      return state;
    } catch (error) {
      console.warn('[InPagePopup] Failed to load state:', error);
      this.clearPersistentState();
      return null;
    }
  }

  /**
   * 清除持久化状态
   */
  private clearPersistentState() {
    this.persistentState = null;
    try {
      localStorage.removeItem(InPagePopup.STORAGE_KEY);
      console.log('[InPagePopup] State cleared');
    } catch (error) {
      console.warn('[InPagePopup] Failed to clear state:', error);
    }
  }

  /**
   * 获取当前模式
   */
  private getCurrentMode(container: HTMLElement): string {
    const modeButtons = container.querySelectorAll('.fillify-mode-btn');
    for (const btn of modeButtons) {
      if (btn.classList.contains('active')) {
        return btn.getAttribute('data-mode') || 'general';
      }
    }
    return 'general';
  }

  /**
   * 恢复弹窗状态
   */
  private restorePopupState(container: HTMLElement, state: PopupPersistentState) {
    try {
      console.log('[InPagePopup] Restoring popup state:', state);

      // 恢复输入内容
      const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
      if (descriptionTextarea && state.description) {
        descriptionTextarea.value = state.description;
      }

      // 恢复语言选择
      const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;
      if (languageSelect && state.language) {
        languageSelect.value = state.language;
      }

      // 恢复项目选择
      const projectSelect = container.querySelector('#fillify-project-select') as HTMLSelectElement;
      if (projectSelect && state.project) {
        projectSelect.value = state.project;
      }

      // 恢复模式选择
      const modeButtons = container.querySelectorAll('.fillify-mode-btn');
      modeButtons.forEach(btn => {
        if (btn.getAttribute('data-mode') === state.mode) {
          btn.classList.add('active');
        } else {
          btn.classList.remove('active');
        }
      });

      // 恢复按钮状态
      this.restoreButtonState(container, state);

      // 恢复推理内容（如果有的话）
      if (state.reasoningContent && (state.buttonState === 'thinking' || state.buttonState === 'loading')) {
        this.updateReasoningBubble(state.reasoningContent, false);
      }
    } catch (error) {
      console.error('[InPagePopup] Error restoring popup state:', error);
      // 如果恢复失败，清除状态防止下次再尝试
      this.clearPersistentState();
    }
  }

  /**
   * 恢复按钮状态
   */
  private restoreButtonState(container: HTMLElement, state: PopupPersistentState) {
    const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
    const btnText = generateBtn?.querySelector('.fillify-button-text') as HTMLElement;
    const loadingAnimation = generateBtn?.querySelector('.fillify-animation') as HTMLElement;
    const sparkleIcon = generateBtn?.querySelector('.fillify-sparkle-icon') as HTMLElement;

    if (!generateBtn || !btnText) return;

    // 清除所有状态类
    generateBtn.classList.remove('loading', 'thinking', 'success');
    generateBtn.disabled = false;

    switch (state.buttonState) {
      case 'loading':
        generateBtn.disabled = true;
        generateBtn.classList.add('loading');
        btnText.textContent = 'Generating...';
        if (sparkleIcon) sparkleIcon.classList.add('hidden');
        if (loadingAnimation) loadingAnimation.style.display = 'block';
        // 显示表单字段动效
        this.showGeneratingEffect();
        break;

      case 'thinking':
        generateBtn.disabled = true;
        generateBtn.classList.add('thinking');
        btnText.textContent = 'Thinking...';
        if (sparkleIcon) sparkleIcon.classList.add('hidden');
        if (loadingAnimation) loadingAnimation.style.display = 'block';
        // 显示表单字段动效
        this.showGeneratingEffect();
        break;

      case 'success':
        generateBtn.classList.add('success');
        btnText.textContent = 'Finish';
        if (sparkleIcon) sparkleIcon.classList.add('hidden');
        if (loadingAnimation) loadingAnimation.style.display = 'none';
        // 设置自动重置定时器
        this.setButtonSuccessState(container, true);
        break;

      default: // 'generate'
        btnText.textContent = 'Generate';
        if (sparkleIcon) sparkleIcon.classList.remove('hidden');
        if (loadingAnimation) loadingAnimation.style.display = 'none';
        break;
    }
  }

  async show() {
    if (this.ui) {
      // UI 已经创建，直接显示
      const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
      if (popup) {
        popup.style.display = 'block';
        popup.style.animation = 'slideInFromBottom 0.3s ease-out forwards';
      }
      return;
    }

    try {
      // 尝试加载持久化状态
      const savedState = this.loadPersistentState();

      // 获取必要的数据
      const [loginStatus, storage] = await Promise.all([
        chrome.runtime.sendMessage({ type: 'getLoginStatus' }),
        chrome.storage.sync.get(['formify_last_mode', 'formify_projects', 'formify_last_language'])
      ]);

      // 异步检测表单字段，但不阻塞UI显示
      let formFields: any[] = [];
      try {
        formFields = await this.detectFormFields();
      } catch (error) {
        console.warn('[InPagePopup] Form field detection failed, but continuing with UI creation:', error);
        // 即使检测失败，也允许UI显示
      }

      const userInfo = loginStatus.isLoggedIn
        ? await chrome.runtime.sendMessage({ type: 'getUserInfo' })
        : null;

      // 使用保存的状态或默认配置
      const config: PopupConfig = {
        mode: savedState?.mode || storage.formify_last_mode || 'general',
        language: savedState?.language || storage.formify_last_language || 'auto',
        projects: storage.formify_projects || [],
        // 关键修复：使用更宽松的条件判断，如果检测失败则默认允许使用
        hasFormFields: formFields.length > 0 || this.shouldAllowUIByDefault()
      };

      // 创建 Shadow Root UI - 使用传入的 context
      this.ui = await createShadowRootUi(this.ctx, {
        name: 'fillify-popup',
        position: 'inline',
        onMount: (uiContainer, shadow, shadowHost) => {
          this.createPopupContent(uiContainer, loginStatus, userInfo, config);
          this.bindEvents(uiContainer, loginStatus, userInfo, config);
          this.setupDragFunctionality(uiContainer);

          // 恢复保存的状态
          if (savedState) {
            this.restorePopupState(uiContainer, savedState);
          }

          return true;
        },
        onRemove: () => {
          this.cleanup();
        }
      });

      this.ui.mount();
      
      // 如果初始检测失败，尝试延迟重新检测表单字段
      if (formFields.length === 0) {
        setTimeout(async () => {
          try {
            const retryFormFields = await this.detectFormFields();
            if (retryFormFields.length > 0) {
              console.log('[InPagePopup] Form fields detected on retry, updating UI state');
              // 更新UI状态但不重新创建整个UI
              this.enableFormElements();
            }
          } catch (error) {
            console.warn('[InPagePopup] Retry form field detection also failed:', error);
          }
        }, 1000);
      }
    } catch (error) {
      console.error('[InPagePopup] Error creating Shadow Root UI:', error);
      throw error;
    }
  }

  /**
   * 判断是否应该默认允许UI使用（用于表单字段检测失败的情况）
   */
  private shouldAllowUIByDefault(): boolean {
    // 在常见的表单网站上默认允许UI使用
    const hostname = window.location.hostname.toLowerCase();
    const allowedHosts = [
      'github.com',
      'mail.google.com', 
      'outlook.live.com',
      'outlook.office.com',
      'gmail.com'
    ];
    
    return allowedHosts.some(host => hostname.includes(host)) || 
           // 或者如果页面包含常见的表单元素，也允许使用
           document.querySelector('input[type="text"], textarea, [contenteditable="true"], [role="textbox"]') !== null;
  }

  /**
   * 启用表单元素（用于延迟检测到表单字段后更新UI状态）
   */
  private enableFormElements(): void {
    if (!this.ui) return;
    
    const container = this.ui.uiContainer;
    const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
    const projectSelect = container.querySelector('#fillify-project-select') as HTMLSelectElement;
    const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;
    const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
    
    if (descriptionTextarea && descriptionTextarea.disabled) {
      descriptionTextarea.disabled = false;
    }
    if (projectSelect && projectSelect.disabled) {
      projectSelect.disabled = false;
    }
    if (languageSelect && languageSelect.disabled) {
      languageSelect.disabled = false;
    }
    if (generateBtn && generateBtn.disabled && !generateBtn.classList.contains('thinking') && !generateBtn.classList.contains('loading')) {
      generateBtn.disabled = false;
    }
  }

  hide() {
    // 在隐藏前保存状态，但只有在非success状态下才保存（success状态应该清除）
    if (this.ui) {
      const container = this.ui.uiContainer;
      const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
      
      if (generateBtn?.classList.contains('success')) {
        // 如果是成功状态，清除保存的状态
        this.clearPersistentState();
      } else {
        // 否则保存当前状态
        let currentButtonState: 'generate' | 'loading' | 'thinking' | 'success' = 'generate';
        if (generateBtn?.classList.contains('loading')) {
          currentButtonState = 'loading';
        } else if (generateBtn?.classList.contains('thinking')) {
          currentButtonState = 'thinking';
        } else if (generateBtn?.classList.contains('success')) {
          currentButtonState = 'success';
        }
        
        this.savePersistentState(currentButtonState);
      }
    }

    // 隐藏推理气泡
    this.hideReasoningBubble();

    if (this.ui) {
      const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
      if (popup) {
        // 添加退出动画
        popup.style.animation = 'slideOutToBottom 0.3s ease-in forwards';

        // 动画完成后移除popup
        setTimeout(() => {
          if (this.ui) {
            this.ui.remove();
            this.ui = null;
          }
        }, 300);
      } else {
        // 如果没有找到popup元素，直接移除
        this.ui.remove();
        this.ui = null;
      }
    }
  }

  toggle() {
    if (this.ui) {
      this.hide();
    } else {
      this.show();
    }
  }

  private createPopupContent(
    container: HTMLElement,
    loginStatus: LoginStatus,
    userInfo: UserInfo | null,
    config: PopupConfig
  ) {
    // 添加样式
    const style = document.createElement('style');
    style.textContent = this.getStyles();
    container.appendChild(style);

    // 创建主容器
    const popup = document.createElement('div');
    popup.className = 'fillify-popup';
    popup.innerHTML = this.getPopupHTML(loginStatus, userInfo, config);

    container.appendChild(popup);
  }

  private getStyles(): string {
    return `
      /* Selectors row */
      .fillify-select-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 8px;
        margin: 0;
      }

      /* Project selector styled like language selector */
      .fillify-project-selector-inline {
        flex: 1;
        display: flex;
        justify-content: flex-start;
      }

      .fillify-project-flex {
        position: relative;
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 0 4px;
        height: 26px;
        border-radius: 4px;
        cursor: pointer;
        background: transparent;
      }

      .fillify-selected-project {
        color: #666;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 180px;
      }

      .fillify-project-flex select {
        position: absolute;
        top: 0; left: 0; width: 100%; height: 100%;
        opacity: 0; cursor: pointer;
      }
      :host {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .fillify-popup {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 380px;
        min-height: 370px;
        background: rgba(245, 245, 245, 0.92); /* same color as #f5f5f5, just adding transparency */
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        overflow: hidden;
        z-index: 10001;
        animation: slideInFromBottom 0.3s ease-out;
      }

      @keyframes slideInFromBottom {
        from {
          transform: translateY(100%);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }

      @keyframes slideOutToBottom {
        from {
          transform: translateY(0);
          opacity: 1;
        }
        to {
          transform: translateY(100%);
          opacity: 0;
        }
      }

      /* Logo animations */
      .fillify-login-logo path[d*="M245.625"] {
        transform-box: fill-box;
        transform-origin: center;
        animation: starPulse 2s ease-in-out infinite;
      }

      .fillify-login-logo path[d*="M245.625"]:nth-of-type(1) {
        animation-delay: 0s;
      }

      .fillify-login-logo path[d*="M245.625"]:nth-of-type(2) {
        animation-delay: -0.6s;
      }

      .fillify-login-logo path[d*="M245.625"]:nth-of-type(3) {
        animation-delay: -1.2s;
      }

      .fillify-login-logo rect[x="340.211"] {
        transform-box: fill-box;
        transform-origin: left;
        animation: rectStretch 3s ease-in-out infinite;
      }

      .fillify-login-logo rect[x="340.211"]:nth-of-type(1) {
        animation-delay: 0s;
      }

      .fillify-login-logo rect[x="340.211"]:nth-of-type(2) {
        animation-delay: -1s;
      }

      .fillify-login-logo rect[x="340.211"]:nth-of-type(3) {
        animation-delay: -2s;
      }

      @keyframes starPulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.15);
        }
      }

      @keyframes rectStretch {
        0%, 100% {
          transform: scaleX(1);
        }
        50% {
          transform: scaleX(1.1);
        }
      }

      /* Header styles */
      .fillify-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 20px;
        background: transparent; /* inherit the popup's frosted look */
      }

      .fillify-header h1 {
        margin: 0;
        color: #2962FF;
        font-size: 22px;
      }

      .fillify-header-right {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      /* Avatar and menu styles */
      .fillify-avatar-menu {
        position: relative;
        cursor: pointer;
      }

      .fillify-avatar-menu img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        cursor: pointer;
      }

      .fillify-menu-popover {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%) translateY(-10px);
        margin-top: 4px;
        background: white;
        border: 1px solid #eee;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        min-width: 100px;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        transition: all 0.2s ease;
        padding: 2px;
      }

      .fillify-menu-popover::before {
        content: '';
        position: absolute;
        top: -4px;
        left: 0;
        right: 0;
        height: 4px;
        background: transparent;
      }

      .fillify-menu-popover::after {
        content: '';
        position: absolute;
        top: -3px;
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid white;
      }

      .fillify-avatar-menu:hover .fillify-menu-popover,
      .fillify-menu-popover:hover {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
        pointer-events: auto;
      }

      .fillify-menu-item {
        padding: 6px 12px;
        font-size: 13px;
        color: #dc2626;
        transition: all 0.2s ease;
        cursor: pointer;
        margin: 2px;
        border-radius: 4px;
      }

      .fillify-menu-item:hover {
        background-color: #f5f5f5;
      }

      /* Button styles */
      button {
        background: none;
        border: none;
        cursor: pointer;
        font-family: inherit;
      }

      .fillify-settings-button {
        padding: 8px;
        border-radius: 4px;
        transition: background 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .fillify-settings-button:hover {
        background: rgba(0, 0, 0, 0.05);
      }

      #fillify-close-btn {
        padding: 4px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s ease;
      }

      #fillify-close-btn:hover {
        background: rgba(0, 0, 0, 0.1);
      }

      /* Form styles */
      .fillify-form-container {
        background: white;
        border-radius: 16px;
        margin: 0 12px 12px 12px;
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }

      /* Mode buttons */
      .fillify-mode-buttons {
        position: relative;
        display: flex;
        gap: 4px;
        padding: 4px;
        background: #f5f5f5;
        border-radius: 8px;
      }

      .fillify-mode-buttons::after {
        content: '';
        position: absolute;
        top: 4px;
        left: 4px;
        width: calc((100% - 16px) / 3);
        height: calc(100% - 8px);
        background: white;
        border-radius: 6px;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        z-index: 0;
      }

      .fillify-mode-btn {
        flex: 1;
        padding: 8px 12px;
        border: none;
        background: transparent;
        color: #666;
        font-size: 14px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        z-index: 1;
        user-select: none;
      }

      .fillify-mode-btn.active {
        color: #2962FF;
      }

      .fillify-mode-btn:hover {
        background: rgba(41, 98, 255, 0.1);
      }

      .fillify-mode-btn.active:hover {
        background: transparent;
      }

      .fillify-mode-btn:active {
        transform: scale(0.97);
      }

      /* Input styles */
      input, textarea, select {
        font-family: inherit;
        box-sizing: border-box;
      }

      textarea {
        width: 100%;
        padding: 14px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 14px;
        line-height: 1.5;
        resize: none;
        transition: all 0.3s ease;
        box-sizing: border-box;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #fff;
        color: #333;
        cursor: text;
        opacity: 1;
      }

      textarea:focus {
        outline: none;
        border-color: #2962FF;
        box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);
      }

      select {
        padding: 10px 14px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fff;
        font-size: 14px;
        color: #333;
        cursor: pointer;
        appearance: none;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"%3E%3Cpolyline points="6 9 12 15 18 9"%3E%3C/polyline%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
      }

      select:focus {
        outline: none;
        border-color: #2962FF;
      }

      /* Primary button */
      .fillify-primary-btn {
        width: 100%;
        padding: 8px;
        border: none;
        border-radius: 8px;
        background: #2962FF;
        color: white;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;
      }

      .fillify-primary-btn:hover:not(.success):not(.loading):not(:disabled) {
        background: #1E4EE3;
      }

      .fillify-primary-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        background: #ccc;
      }

      .fillify-primary-btn.loading {
        background: #1E4EE3;
        cursor: not-allowed;
      }

      .fillify-primary-btn.thinking {
        background: #1E4EE3;
       cursor: not-allowed;
      }

      .fillify-primary-btn.thinking .fillify-animation {
        display: block;
      } 

      .fillify-primary-btn.success {
        background: #2962FF;
        cursor: default;
        color: white;
      }

      /* Sparkle animation */
      @keyframes sparkle {
        0%, 100% {
          transform: scale(1);
          opacity: 0.9;
        }
        50% {
          transform: scale(1.1);
          opacity: 1;
        }
      }

      .fillify-sparkle-icon {
        width: 16px;
        height: 16px;
        color: inherit;
        opacity: 0.9;
        animation: sparkle 2s ease-in-out infinite;
        margin-left: 4px;
        transition: opacity 0.3s ease;
        transform-box: fill-box;
        transform-origin: center;
      }

      .fillify-sparkle-icon.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .fillify-primary-btn:hover .fillify-sparkle-icon {
        animation: sparkle-hover 1s ease-in-out infinite;
      }

      @keyframes sparkle-hover {
        0%, 100% {
          transform: scale(1.1) rotate(0deg);
          opacity: 1;
        }
        50% {
          transform: scale(1.2) rotate(10deg);
          opacity: 0.9;
        }
      }

      /* Loading animation */
      .fillify-primary-btn .fillify-animation {
        display: none;
        position: absolute;
        border-radius: 100%;
        animation: ripple 0.6s linear infinite;
      }

      .fillify-primary-btn.loading .fillify-animation {
        display: block;
      }

      @keyframes ripple {
        0% {
          box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1),
                     0 0 0 40px rgba(255, 255, 255, 0.1),
                     0 0 0 80px rgba(255, 255, 255, 0.1),
                     0 0 0 120px rgba(255, 255, 255, 0.1);
        }
        100% {
          box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.1),
                     0 0 0 80px rgba(255, 255, 255, 0.1),
                     0 0 0 120px rgba(255, 255, 255, 0.1),
                     0 0 0 160px rgba(255, 255, 255, 0);
        }
      }

      /* Status message */
      .fillify-status-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        pointer-events: none;
        z-index: 10002;
      }

      .fillify-status-message {
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        max-width: 80%;
        text-align: center;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
        pointer-events: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .fillify-status-message.show {
        opacity: 1;
        transform: translateY(0);
      }

      .fillify-status-message.error {
        background: rgba(244, 67, 54, 0.9);
      }

      .fillify-status-message.success {
        background: rgba(76, 175, 80, 0.9);
      }

      .fillify-status-message.info {
        background: rgba(33, 150, 243, 0.9);
      }

      /* Disabled states */
      textarea:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
        opacity: 0.6;
      }

      select:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
      }

      /* Login prompt styles */
      .fillify-login-prompt {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.98);
        min-height: 370px;
        /* Add inner spacing so the login card isn't flush with popup edges */
        padding: 16px 16px; /* vertical & horizontal padding */
        box-sizing: border-box;  /* ensure padding doesn't overflow */
      }

      .fillify-login-content {
        text-align: center;
        padding: 30px 15px;
        border-radius: 12px;
        background: white;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
        width: 90%;
        max-width: 320px;
      }

      .fillify-sign-in-btn {
        background: #1D5DF4;
        color: white;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
        margin-bottom: 12px;
      }

      .fillify-sign-in-btn:hover {
        background: #1850D8;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(29, 93, 244, 0.2);
      }

      .fillify-sign-in-btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(29, 93, 244, 0.2);
      }

      .fillify-skip-login-text {
        display: block;
        color: #666;
        font-size: 14px;
        text-decoration: none;
        margin-top: 12px;
        cursor: pointer;
        transition: color 0.2s ease;
      }

      .fillify-skip-login-text:hover {
        color: #1D5DF4;
        text-decoration: underline;
      }

      /* Language selector */
      .fillify-language-selector {
        margin: 0;
        display: flex;
        justify-content: flex-end;
      }

      .fillify-language-flex {
        position: relative;
        display: flex;
        align-items: center;
        gap: 2px;
        padding: 0 4px;
        height: 26px;
        border-radius: 4px;
        cursor: pointer;
      }

      .fillify-selected-language {
        color: #666;
        font-size: 12px;
        margin-right: 2px;
      }

      /* Project selector */
      .fillify-project-selector {
        margin: 0;
      }

      .fillify-select-wrapper {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .fillify-add-project-btn {
        padding: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fff;
        color: #666;
        font-size: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 42px;
        transition: all 0.2s ease;
      }

      .fillify-add-project-btn:hover {
        background: #f5f5f5;
        border-color: #ccc;
      }

      /* Debug menu */
      .fillify-debug-menu {
        position: absolute;
        right: 36px; /* sit near the debug button */
        top: 40px;
        background: #fff;
        border: 1px solid #eee;
        border-radius: 8px;
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        min-width: 180px;
        z-index: 10010;
        padding: 6px;
        display: none;
      }
      .fillify-debug-menu.show { display: block; }
      .fillify-debug-item {
        font-size: 13px;
        color: #333;
        padding: 8px 10px;
        border-radius: 6px;
        cursor: pointer;
      }
      .fillify-debug-item:hover { background: #f5f5f5; }

      /* Confetti canvas */
      .fillify-confetti-canvas {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 1000;
        width: 100%;
        height: 100%;
      }

      .fillify-reasoning-close {
        position: absolute;
        top: 8px;
        right: 8px;
        background: transparent;
        border: none;
        font-size: 16px;
        color: #999;
        cursor: pointer;
        z-index: 3;
        line-height: 1;
      }

      .fillify-reasoning-close:hover {
        color: #333;
      }




    `;
  }

  private getPopupHTML(loginStatus: LoginStatus, userInfo: UserInfo | null, config: PopupConfig): string {
    const isLoggedIn = loginStatus?.isLoggedIn || false;
    const skipLogin = loginStatus?.skipLogin || false;
    const showLoginPrompt = !isLoggedIn && !skipLogin;

    if (showLoginPrompt) {
      return this.getLoginPromptHTML();
    } else {
      return this.getMainPopupHTML(isLoggedIn, userInfo, config);
    }
  }

  private getLoginPromptHTML(): string {
    return `
      <div class="fillify-login-prompt">
        <div class="fillify-login-content">
          <div class="fillify-logo-container" style="margin-bottom: 1rem;">
            <svg class="fillify-login-logo" width="120" height="120" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 120px; height: 120px; margin-bottom: 1rem;">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M811.311 65H122C94.3858 65 72 87.3858 72 115V908C72 935.614 94.3858 958 122 958H902.481C930.096 958 952.481 935.614 952.481 908V206.17L811.311 65Z" fill="url(#paint0_linear_46_10)"/>
              <g style="mix-blend-mode:hard-light">
                <path d="M956 217H814V75L885 146L956 217Z" fill="#D9D9D9"/>
              </g>
              <rect x="340.211" y="344.847" width="504.457" height="81.6033" fill="white"/>
              <rect x="340.211" y="508.054" width="504.457" height="81.6033" fill="white"/>
              <rect x="340.211" y="671.261" width="504.457" height="81.6033" fill="white"/>
              <path d="M245.625 333.72L260.152 372.977L299.409 387.504L260.152 402.03L245.625 441.288L231.099 402.03L191.841 387.504L231.099 372.977L245.625 333.72Z" fill="white"/>
              <path d="M245.625 496.926L260.152 536.184L299.409 550.71L260.152 565.237L245.625 604.494L231.099 565.237L191.841 550.71L231.099 536.184L245.625 496.926Z" fill="white"/>
              <path d="M245.625 660.133L260.152 699.39L299.409 713.917L260.152 728.443L245.625 767.701L231.099 728.443L191.841 713.917L231.099 699.39L245.625 660.133Z" fill="white"/>
              <defs>
                <linearGradient id="paint0_linear_46_10" x1="952" y1="65" x2="71.9999" y2="958" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#1C5BEE"/>
                  <stop offset="1" stop-color="#5C82DB"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
          <h2 style="color: #1D5DF4; font-size: 1.5rem; margin: 0 0 0.5rem; font-weight: 600;">Welcome to Fillify</h2>
          <p style="color: #666; margin: 0 0 1.5rem; font-size: 0.9rem;">Please sign in to access all features</p>
          <button id="fillify-signin-btn" class="fillify-sign-in-btn">Sign in</button>
          <a href="#" id="fillify-skip-login" class="fillify-skip-login-text">Skip sign in</a>
        </div>
      </div>
    `;
  }

  private getMainPopupHTML(isLoggedIn: boolean, userInfo: UserInfo | null, config: PopupConfig): string {
    const userAvatarUrl = userInfo?.user?.picture_url || '';
    const languageMap: Record<string, string> = {
      'auto': 'Auto',
      'id': 'Bahasa Indonesia',
      'ms': 'Bahasa Melayu',
      'da': 'Dansk',
      'de': 'Deutsch',
      'en': 'English',
      'es': 'Español',
      'fr': 'Français',
      'it': 'Italiano',
      'nl': 'Nederlands',
      'no': 'Norsk',
      'pl': 'Polski',
      'pt': 'Português',
      'ro': 'Română',
      'fi': 'Suomi',
      'sv': 'Svenska',
      'vi': 'Tiếng Việt',
      'tr': 'Türkçe',
      'hu': 'Magyar',
      'cs': 'Čeština',
      'uk': 'Українська',
      'ru': 'Русский',
      'bg': 'Български',
      'ar': 'العربية',
      'fa': 'فارسی',
      'he': 'עִבְרִית',
      'hi': 'हिन्दी',
      'th': 'ไทย',
      'ja': '日本語',
      'zh-CN': '中文（简体）',
      'zh-TW': '中文（繁體）',
      'ko': '한국어'
    };

    const selectedLanguageText = languageMap[config.language] || config.language;

    const getPlaceholder = (mode: string) => {
      switch (mode) {
        case 'bugReport': return 'Enter bug description';
        case 'email': return 'Enter email content description';
        default: return 'Enter description';
      }
    };

    return `
      <div class="fillify-container">

        <!-- Header -->
        <div class="fillify-header">
          <h1>Fillify</h1>
          <div class="fillify-header-right">
            <div class="fillify-login-status" style="display: flex; align-items: center; margin-right: 4px; font-size: 14px; color: #666;">
              <div class="fillify-avatar-menu">
                ${isLoggedIn ? `
                  <img src="${userAvatarUrl}" title="Go to Dashboard" id="fillify-avatar" />
                  <div class="fillify-menu-popover">
                    <div class="fillify-menu-item" id="fillify-signout">Sign out</div>
                  </div>
                ` : `
                  <div style="cursor: pointer;" title="Sign in" id="fillify-signin-icon">
                    <svg width="24" height="24" viewBox="0 0 1024 1024" style="vertical-align: middle;">
                      <path d="M512 64C264.8 64 64 264.8 64 512s200.8 448 448 448 448-200.8 448-448S759.2 64 512 64zM384.8 376c4-64 56-115.2 120-119.2 74.4-4 135.2 55.2 135.2 128 0 70.4-57.6 128-128 128-73.6 0-132-62.4-127.2-136.8zM768 746.4c0 12-9.6 21.6-21.6 21.6H278.4c-12 0-21.6-9.6-21.6-21.6v-64c0-84.8 170.4-128 255.2-128 84.8 0 255.2 42.4 255.2 128l0.8 64z" fill="#333333"/>
                    </svg>
                  </div>
                `}
              </div>
            </div>
            <button class="fillify-settings-button" id="fillify-settings" title="Settings">
              <div class="fillify-menu-icon" style="width: 18px; height: 14px; position: relative; display: flex; flex-direction: column; justify-content: space-between; pointer-events: none;">
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px; transition: all 0.2s ease; pointer-events: none;"></span>
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px; transition: all 0.2s ease; pointer-events: none;"></span>
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px; transition: all 0.2s ease; pointer-events: none;"></span>
              </div>
            </button>
          <button id="fillify-close-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="#666">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
          <!-- ===== DEBUG BUTTON START ===== -->
          <button id="fillify-debug-btn" style="margin-right: 8px;" title="Debug Bubble">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="#666">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
            </svg>
          </button>
          <div id="fillify-debug-menu" class="fillify-debug-menu">
            <div class="fillify-debug-item" data-action="reasoning">Test reasoning bubble</div>
            <div class="fillify-debug-item" data-action="login">Test login screen</div>
          </div>
          <!-- ===== DEBUG BUTTON END ===== -->
          </div>
        </div>

        <!-- Form container -->
        <form id="fillify-form-config" class="fillify-form-container">
          <!-- Mode buttons -->
          <div class="fillify-mode-buttons" style="background: #f5f5f5;">
            <button type="button" class="fillify-mode-btn ${config.mode === 'general' ? 'active' : ''}" data-mode="general">General</button>
            <button type="button" class="fillify-mode-btn ${config.mode === 'email' ? 'active' : ''}" data-mode="email">Email</button>
            <button type="button" class="fillify-mode-btn ${config.mode === 'bugReport' ? 'active' : ''}" data-mode="bugReport">Bug Report</button>
          </div>

          <!-- Description input -->
          <div class="fillify-description-input" style="flex-grow: 1; display: flex; flex-direction: column; gap: 0;">
            <div class="fillify-textarea-wrapper" style="width: 100%; margin-bottom: 2px;">
              <textarea 
                id="fillify-description" 
                placeholder="${getPlaceholder(config.mode)}" 
                style="height: 200px;"
                ${!config.hasFormFields ? 'disabled' : ''}
              ></textarea>
            </div>
            <!-- Selectors row: Project (left, only in Bug Report) + Language (right) -->
            <div class="fillify-select-row">
              ${config.mode === 'bugReport' ? `
              <div class="fillify-project-selector-inline">
                <div class="fillify-project-flex">
                  <!-- simple folder icon -->
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width:16px;height:16px;">
                    <path d="M3 7a2 2 0 012-2h4l2 2h6a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V7z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <div class="fillify-selected-project">
                    ${config.projects && config.projects.length ? 'Select project' : 'No projects'}
                  </div>
                  <select id="fillify-project-select" ${!config.hasFormFields ? 'disabled' : ''}>
                    <option value="">${config.projects && config.projects.length ? 'Select project (Optional)' : 'No projects available'}</option>
                    ${config.projects.map(project => `
                      <option value="${project.id}">${project.name}</option>
                    `).join('')}
                    <option disabled>────────────</option>
                    <option value="__add_project__">Add project…</option>
                  </select>
                </div>
              </div>
              ` : '<div style="flex:1;"></div>'}

              <div class="fillify-language-selector">
                <div class="fillify-language-flex">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px;">
                    <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M3.6001 9H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M3.6001 15H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 21C13.6569 21 15 16.9706 15 12C15 7.02944 13.6569 3 12 3C10.3432 3 9 7.02944 9 12C9 16.9706 10.3432 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <div class="fillify-selected-language">${selectedLanguageText}</div>
                  <select id="fillify-language-select" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer;">
                    ${Object.entries(languageMap).map(([value, label]) => `
                      <option value="${value}" ${config.language === value ? 'selected' : ''}>${label}</option>
                    `).join('')}
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Generate button -->
          <button id="fillify-fill-button" type="submit" class="fillify-primary-btn" ${!config.hasFormFields ? 'disabled' : ''}>
            <div class="fillify-button-content" style="display: flex; align-items: center; gap: 6px; position: relative; z-index: 1;">
              <span class="fillify-button-text">Generate</span>
              <svg class="fillify-sparkle-icon" viewBox="0 0 24 24" fill="currentColor">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813a3.75 3.75 0 002.576-2.576l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"></path>
              </svg>
            </div>
            <div class="fillify-animation" style="display: none; position: absolute; border-radius: 100%; animation: ripple 0.6s linear infinite;"></div>
          </button>
        </form>

        <!-- Status message container -->
        <div class="fillify-status-container">
          <div id="fillify-status-message" class="fillify-status-message"></div>
        </div>

        <!-- Confetti Canvas -->
        <canvas class="fillify-confetti-canvas"></canvas>
      </div>
    `;
  }

  private async bindEvents(
    container: HTMLElement,
    loginStatus: LoginStatus,
    userInfo: UserInfo | null,
    config: PopupConfig
  ) {
    // Close button
    const closeBtn = container.querySelector('#fillify-close-btn');
    closeBtn?.addEventListener('click', () => this.hide());

    // Sign in button
    const signinBtn = container.querySelector('#fillify-signin-btn');
    signinBtn?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openLoginPage' });
      this.hide();
    });

    // Skip login
    const skipBtn = container.querySelector('#fillify-skip-login');
    skipBtn?.addEventListener('click', async (e) => {
      e.preventDefault();
      await chrome.runtime.sendMessage({ type: 'setSkipLogin', skip: true });
      this.refreshContent();
    });

    // Avatar click
    const avatar = container.querySelector('#fillify-avatar');
    avatar?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openDashboard' });
      this.hide();
    });

    // Sign in icon click
    const signinIcon = container.querySelector('#fillify-signin-icon');
    signinIcon?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openLoginPage' });
      this.hide();
    });

    // Debug button with popover menu
    const debugBtn = container.querySelector('#fillify-debug-btn');
    const debugMenu = container.querySelector('#fillify-debug-menu');
    debugBtn?.addEventListener('click', (e) => {
      e.stopPropagation();
      debugMenu?.classList.toggle('show');
    });

    // Click outside to close
    document.addEventListener('click', () => debugMenu?.classList.remove('show'), { once: true });

    // Delegate menu item clicks
    debugMenu?.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const action = target.getAttribute('data-action');
      if (!action) return;
      debugMenu.classList.remove('show');
      if (action === 'reasoning') {
        this.showDebugBubble();
      } else if (action === 'login') {
        this.showLoginTest();
      }
    });

    // Sign out
    const signoutBtn = container.querySelector('#fillify-signout');
    signoutBtn?.addEventListener('click', async () => {
      try {
        await chrome.runtime.sendMessage({ type: 'signOut' });
        this.refreshContent();
      } catch (error) {
        this.showStatusMessage('Failed to sign out', 'error');
      }
    });

    // Settings button
    const settingsBtn = container.querySelector('#fillify-settings');
    settingsBtn?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openSettings' });
      this.hide();
    });




    // Mode buttons
    const modeButtons = container.querySelectorAll('.fillify-mode-btn');
    modeButtons.forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const target = e.target as HTMLElement;
        const mode = target.getAttribute('data-mode');
        if (mode) {
          await chrome.storage.sync.set({ formify_last_mode: mode });
          this.refreshContent();
        }
      });
    });

    // Language selector
    const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;
    languageSelect?.addEventListener('change', async (e) => {
      const target = e.target as HTMLSelectElement;
      await chrome.storage.sync.set({ formify_last_language: target.value });

      // Update display text using the selected option's label to avoid map mismatch
      const selectedLanguageEl = container.querySelector('.fillify-selected-language');
      if (selectedLanguageEl) {
        const selectedOption = (target as HTMLSelectElement).selectedOptions?.[0];
        selectedLanguageEl.textContent = selectedOption ? selectedOption.text : target.value;
      }
    });

    // Project selector change
    const projectSelectInline = container.querySelector('#fillify-project-select') as HTMLSelectElement;
    projectSelectInline?.addEventListener('change', async (e) => {
      const sel = e.target as HTMLSelectElement;
      if (sel.value === '__add_project__') {
        // Open project settings, then refresh
        chrome.runtime.sendMessage({ type: 'openProjectSettings' });
        // Reset to placeholder so the UI doesn't keep the special value
        sel.value = '';
      }
      // Update visible selected text
      const selectedTextEl = container.querySelector('.fillify-selected-project') as HTMLElement;
      if (selectedTextEl) {
        const opt = sel.selectedOptions?.[0];
        selectedTextEl.textContent = opt ? opt.text : (sel.value ? sel.value : (sel.querySelector('option')?.textContent || 'Select project'));
      }
    });

    // Form submit
    const form = container.querySelector('#fillify-form-config');
    const generateBtn = container.querySelector('#fillify-fill-button');

    const handleSubmit = async (e: Event) => {
      e.preventDefault();
      await this.handleGenerate(container);
    };

    form?.addEventListener('submit', handleSubmit);
    generateBtn?.addEventListener('click', handleSubmit);

    // 添加简单的输入事件隔离 - 只针对描述文本框
    const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
    if (descriptionTextarea) {
      descriptionTextarea.addEventListener('keydown', (e) => {
        e.stopPropagation();
      });
      descriptionTextarea.addEventListener('keyup', (e) => {
        e.stopPropagation();
      });
      descriptionTextarea.addEventListener('input', (e) => {
        e.stopPropagation();
        
        // 如果用户修改了输入内容且按钮处于成功状态，立即重置按钮
        if (container.querySelector('#fillify-fill-button')?.classList.contains('success')) {
          this.resetButtonToGenerateState(container);
        } else {
          // 否则更新保存的状态（保持当前状态但更新内容）
          const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
          let currentButtonState: 'generate' | 'loading' | 'thinking' | 'success' = 'generate';
          if (generateBtn?.classList.contains('loading')) {
            currentButtonState = 'loading';
          } else if (generateBtn?.classList.contains('thinking')) {
            currentButtonState = 'thinking';
          } else if (generateBtn?.classList.contains('success')) {
            currentButtonState = 'success';
          }
          this.savePersistentState(currentButtonState);
        }
      });
    }

    // Update mode background
    this.updateModeBackground(container, config.mode);
  }

  /**
   * 获取当前描述输入内容
   */
  private getCurrentDescription(): string {
    if (!this.ui) return '';
    const descriptionTextarea = this.ui.uiContainer.querySelector('#fillify-description') as HTMLTextAreaElement;
    return descriptionTextarea?.value || '';
  }

  /**
   * 获取当前语言选择
   */
  private getCurrentLanguage(): string {
    if (!this.ui) return '';
    const languageSelect = this.ui.uiContainer.querySelector('#fillify-language-select') as HTMLSelectElement;
    return languageSelect?.value || '';
  }

  /**
   * 获取当前项目选择
   */
  private getCurrentProject(): string {
    if (!this.ui) return '';
    const projectSelect = this.ui.uiContainer.querySelector('#fillify-project-select') as HTMLSelectElement;
    return projectSelect?.value || '';
  }

  /**
   * 恢复用户输入内容
   */
  private restoreUserInputs(description: string, language: string, project: string) {
    if (!this.ui) return;

    // 恢复描述内容
    const descriptionTextarea = this.ui.uiContainer.querySelector('#fillify-description') as HTMLTextAreaElement;
    if (descriptionTextarea && description) {
      descriptionTextarea.value = description;
    }

    // 恢复语言选择
    const languageSelect = this.ui.uiContainer.querySelector('#fillify-language-select') as HTMLSelectElement;
    if (languageSelect && language) {
      languageSelect.value = language;
    }

    // 恢复项目选择
    const projectSelect = this.ui.uiContainer.querySelector('#fillify-project-select') as HTMLSelectElement;
    if (projectSelect && project) {
      projectSelect.value = project;
    }
  }

  private async refreshContent() {
    if (!this.ui) return;

    // 保存当前用户输入的内容
    const currentDescription = this.getCurrentDescription();
    const currentLanguage = this.getCurrentLanguage();
    const currentProject = this.getCurrentProject();

    // Get fresh data
    const [loginStatus, storage] = await Promise.all([
      chrome.runtime.sendMessage({ type: 'getLoginStatus' }),
      chrome.storage.sync.get(['formify_last_mode', 'formify_projects', 'formify_last_language'])
    ]);

    // 异步检测表单字段，但不阻塞UI更新
    let formFields: any[] = [];
    try {
      formFields = await this.detectFormFields();
    } catch (error) {
      console.warn('[InPagePopup] Form field detection failed during refresh, but continuing:', error);
    }

    const userInfo = loginStatus.isLoggedIn
      ? await chrome.runtime.sendMessage({ type: 'getUserInfo' })
      : null;

    const config: PopupConfig = {
      mode: storage.formify_last_mode || 'general',
      language: storage.formify_last_language || 'auto',
      projects: storage.formify_projects || [],
      // 关键修复：使用更宽松的条件判断
      hasFormFields: formFields.length > 0 || this.shouldAllowUIByDefault()
    };

    // Get container and update content
    if (this.ui) {
      const popup = this.ui.uiContainer.querySelector('.fillify-popup');
      if (popup) {
        popup.innerHTML = this.getPopupHTML(loginStatus, userInfo, config);
        this.bindEvents(this.ui.uiContainer, loginStatus, userInfo, config);

        // 恢复用户输入的内容
        this.restoreUserInputs(currentDescription, currentLanguage, currentProject);
        
        // 如果检测失败但UI应该可用，确保表单元素被启用
        if (formFields.length === 0 && this.shouldAllowUIByDefault()) {
          this.enableFormElements();
        }
      }
    }
  }

  private async handleGenerate(container: HTMLElement) {

    const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
    const btnText = container.querySelector('.fillify-button-text') as HTMLElement;
    const sparkleIcon = container.querySelector('.fillify-sparkle-icon') as HTMLElement;
    const loadingAnimation = container.querySelector('.fillify-animation') as HTMLElement;
    const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
    const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;
    const projectSelect = container.querySelector('#fillify-project-select') as HTMLSelectElement;

    if (!generateBtn || !btnText || !sparkleIcon || !loadingAnimation || !descriptionTextarea) {
      console.error('Required elements not found in popup');
      return;
    }

    const description = descriptionTextarea.value.trim();
    if (!description) {
      this.showStatusMessage('Please enter a description', 'error');
      return;
    }

    try {
      // Show loading state
      generateBtn.disabled = true;
      generateBtn.classList.add('loading');
      btnText.textContent = 'Generating...';
      sparkleIcon.classList.add('hidden');
      loadingAnimation.style.display = 'block';

      // Get current mode
      const storage = await chrome.storage.sync.get(['formify_last_mode']);
      const currentMode = storage.formify_last_mode || 'general';

      // Detect form fields
      const formFields = await this.detectFormFields();
      if (!formFields.length) {
        throw new Error('No form fields detected on this page');
      }

      // Show generating effect
      this.showGeneratingEffect();

      // 保存loading状态
      this.savePersistentState('loading');

      // Send AI request
      const aiResponse = await chrome.runtime.sendMessage({
        type: 'aiRequest',
        prompt: description,
        options: {
          mode: currentMode,
          projectId: projectSelect?.value || '',
          language: languageSelect?.value || 'auto',
          description: description,
          formFields: formFields.map(field => ({
            type: field.type,
            id: field.id,
            name: field.name,
            placeholder: field.placeholder,
            label: field.label
          }))
        }
      });

      console.log('[InPagePopup] AI Response received:', aiResponse);
      console.log('[InPagePopup] AI Response data structure:', {
        hasData: !!aiResponse?.data,
        dataKeys: aiResponse?.data ? Object.keys(aiResponse.data) : [],
        hasReasoning: !!aiResponse?.data?.reasoning,
        reasoningLength: aiResponse?.data?.reasoning?.length || 0,
        reasoning: aiResponse?.data?.reasoning,
        isStreaming: !!aiResponse?.streaming
      });

      if (!aiResponse?.success) {
        throw new Error(aiResponse?.error || 'Failed to generate content');
      }

      // 检查是否为流式响应
      if (aiResponse.streaming) {
        console.log('[InPagePopup] Streaming response detected, waiting for completion...');
        // 流式响应，等待 streamingComplete 消息
        // 不需要在这里处理，由 handleStreamingComplete 处理
        return;
      }

      // 非流式响应，直接处理
      if (!aiResponse.data) {
        throw new Error('No data received from AI response');
      }

      // 检查非流式响应是否包含reasoning（比如一次性返回的推理结果）
      if (aiResponse.data.reasoning) {
        console.log('[InPagePopup] Non-streaming response contains reasoning, showing reasoning bubble');
        this.updateReasoningBubble(aiResponse.data.reasoning, true);
        
        // 给用户一些时间查看推理内容，然后继续处理
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Fill form
      await this.fillForm(aiResponse.data.data || aiResponse.data);

      // 使用新的按钮状态管理方法
      this.setButtonSuccessState(container, true); // 启用自动重置

      // Show confetti
      this.createConfetti(container);

      // this.showStatusMessage('Form filled successfully!', 'success');

      // 移除自动关闭弹窗的逻辑，让用户手动关闭
      // setTimeout(() => {
      //   this.hide();
      // }, 1500);

    } catch (error) {
      console.error('Error in handleGenerate:', error);
      this.showStatusMessage(error instanceof Error ? error.message : 'An error occurred', 'error');
    } finally {
      // Restore button state if not success
      if (!generateBtn.classList.contains('success')) {
        generateBtn.disabled = false;
        generateBtn.classList.remove('loading');
        generateBtn.classList.remove('thinking'); // 关键：确保退出 thinking
        btnText.textContent = 'Generate';
        if (sparkleIcon) sparkleIcon.classList.remove('hidden');
        if (loadingAnimation) loadingAnimation.style.display = 'none';
      }

      // Clear generating effect
      this.removeGeneratingEffect();
    }
  }

  private async detectFormFields(): Promise<FormField[]> {
    // 直接通过 window 对象获取检测到的表单字段
    // 这是一个临时解决方案，我们在页面上暴露一个全局函数
    return new Promise((resolve) => {
      // 使用 postMessage 与 content script 通信
      const messageId = `detect-fields-${Date.now()}`;

      const handleMessage = (event: MessageEvent) => {
        if (event.source === window && event.data?.type === 'DETECT_FIELDS_RESPONSE' && event.data?.id === messageId) {
          window.removeEventListener('message', handleMessage);
          resolve(event.data.fields || []);
        }
      };

      window.addEventListener('message', handleMessage);

      // 发送检测请求
      window.postMessage({
        type: 'DETECT_FIELDS_REQUEST',
        id: messageId
      }, '*');

      // 超时处理
      setTimeout(() => {
        window.removeEventListener('message', handleMessage);
        resolve([]);
      }, 1000);
    });
  }

  private showGeneratingEffect() {
    // Send message to content script to show generating effect
    chrome.runtime.sendMessage({ type: 'showGeneratingEffect' });
  }

  private removeGeneratingEffect() {
    // Send message to content script to remove generating effect
    chrome.runtime.sendMessage({ type: 'removeGeneratingEffect' });
  }

  private async fillForm(formData: any) {
    // Send message to content script to fill form
    return new Promise<void>((resolve, reject) => {
      chrome.runtime.sendMessage({
        type: 'fillForm',
        data: formData
      }, (response) => {
        if (response?.success) {
          resolve();
        } else {
          reject(new Error(response?.error || 'Failed to fill form'));
        }
      });
    });
  }

  private showStatusMessage(message: string, type: 'info' | 'error' | 'success' = 'info') {
    if (!this.ui) return;

    const container = this.ui.uiContainer;
    const statusMessage = container?.querySelector('#fillify-status-message') as HTMLElement;

    if (statusMessage) {
      statusMessage.textContent = message;
      statusMessage.className = `fillify-status-message show ${type}`;

      // Auto-hide
      setTimeout(() => {
        statusMessage.classList.remove('show');
      }, 3000);
    }
  }

  private updateModeBackground(container: HTMLElement, mode: string) {
    // 计算背景的transform值
    let transformValue = 'translateX(0)';
    if (mode === 'email') {
      transformValue = 'translateX(calc(100% + 4px))';
    } else if (mode === 'bugReport') {
      transformValue = 'translateX(calc(200% + 8px))';
    }

    // 获取或创建样式标签
    let modeStyleTag = container.querySelector('#fillify-mode-style') as HTMLStyleElement;
    if (!modeStyleTag) {
      modeStyleTag = document.createElement('style');
      modeStyleTag.id = 'fillify-mode-style';
      container.appendChild(modeStyleTag);
    }

    // 更新样式以实现平滑的背景切换动画
    modeStyleTag.textContent = `
      .fillify-mode-buttons::after {
        transform: ${transformValue} !important;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      }
      
      /* 确保模式按钮的激活状态正确显示 */
      .fillify-mode-btn.active {
        color: #2962FF !important;
        font-weight: 500 !important;
      }
      
      /* 添加按钮悬停效果的细微调整 */
      .fillify-mode-btn:not(.active):hover {
        background: rgba(41, 98, 255, 0.1) !important;
        transform: scale(1.02) !important;
      }
      
      .fillify-mode-btn.active:hover {
        background: transparent !important;
        transform: none !important;
      }
      
      /* 增强按钮点击动画 */
      .fillify-mode-btn:active {
        transform: scale(0.97) !important;
        transition: transform 0.1s ease !important;
      }
    `;

    // 更新按钮的active状态
    const modeButtons = container.querySelectorAll('.fillify-mode-btn');
    modeButtons.forEach(button => {
      const buttonMode = button.getAttribute('data-mode');
      if (buttonMode === mode) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
  }

  private setupDragFunctionality(container: HTMLElement) {
    const popup = container.querySelector('.fillify-popup') as HTMLElement;
    if (!popup) return;

    const handleMouseDown = (e: MouseEvent) => {
      const target = e.target as HTMLElement;

      // Exclude interactive elements
      if (target.tagName === 'BUTTON' ||
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.tagName === 'SELECT' ||
        target.closest('button, input, textarea, select, .fillify-menu-popover')) {
        return;
      }

      e.preventDefault();

      this.dragStartX = e.clientX;
      this.dragStartY = e.clientY;

      const rect = popup.getBoundingClientRect();
      this.popupStartX = rect.left;
      this.popupStartY = rect.top;

      this.isLongPressTriggered = false;

      // Long press timer - 优化为和原版相同的300ms
      this.longPressTimer = window.setTimeout(() => {
        this.isLongPressTriggered = true;
        this.startDragging(popup);
      }, 300);

      document.addEventListener('mousemove', this.handleMouseMove.bind(this));
      document.addEventListener('mouseup', this.handleMouseUp.bind(this));
    };

    popup.addEventListener('mousedown', handleMouseDown);
  }

  private handleMouseMove = (e: MouseEvent) => {
    if (this.longPressTimer && !this.isLongPressTriggered) {
      const moveThreshold = 5;
      if (Math.abs(e.clientX - this.dragStartX) > moveThreshold ||
        Math.abs(e.clientY - this.dragStartY) > moveThreshold) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }
    }

    if (this.isDragging && this.ui) {
      e.preventDefault();
      e.stopPropagation();

      const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
      if (!popup) return;

      const deltaX = e.clientX - this.dragStartX;
      const deltaY = e.clientY - this.dragStartY;

      let newX = this.popupStartX + deltaX;
      let newY = this.popupStartY + deltaY;

      // Boundary checks
      const rect = popup.getBoundingClientRect();
      const maxX = window.innerWidth - rect.width;
      const maxY = window.innerHeight - rect.height;

      newX = Math.max(0, Math.min(newX, maxX));
      newY = Math.max(0, Math.min(newY, maxY));

      popup.style.left = `${newX}px`;
      popup.style.top = `${newY}px`;
      popup.style.right = 'auto';
      popup.style.bottom = 'auto';
    }
  };

  private handleMouseUp = () => {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    if (this.isDragging) {
      this.stopDragging();
    }

    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
  };

  private startDragging(popup: HTMLElement) {
    this.isDragging = true;
    popup.style.cursor = 'grabbing';
    popup.style.transform = 'scale(1.02)';
    popup.style.boxShadow = '0 25px 70px rgba(0, 0, 0, 0.4)';
    popup.style.transition = 'transform 0.1s ease, box-shadow 0.1s ease';

    document.body.style.userSelect = 'none';
  }

  private stopDragging() {
    if (!this.ui) return;

    const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
    if (!popup) return;

    this.isDragging = false;
    popup.style.cursor = '';
    popup.style.transform = '';
    popup.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.3)';
    popup.style.transition = 'transform 0.15s ease, box-shadow 0.15s ease';

    document.body.style.userSelect = '';

    setTimeout(() => {
      if (popup) {
        popup.style.transition = '';
      }
    }, 150);
  }

  private createConfetti(container: HTMLElement) {
    const canvas = container.querySelector('.fillify-confetti-canvas') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const popup = container.querySelector('.fillify-popup') as HTMLElement;
    if (!popup) return;

    const rect = popup.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    // Confetti implementation similar to the original
    const confettiConfig = {
      confettiCount: 20,
      sequinCount: 10,
      colors: [
        { front: '#7b5cff', back: '#6245e0' },
        { front: '#b3c7ff', back: '#8fa5e5' },
        { front: '#5c86ff', back: '#345dd1' }
      ]
    };

    let confetti: any[] = [];

    // Simple confetti implementation
    for (let i = 0; i < confettiConfig.confettiCount; i++) {
      confetti.push({
        x: canvas.width / 2,
        y: canvas.height / 2,
        vx: (Math.random() - 0.5) * 10,
        vy: Math.random() * -10 - 5,
        color: confettiConfig.colors[Math.floor(Math.random() * confettiConfig.colors.length)].front,
        size: Math.random() * 6 + 4
      });
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      confetti.forEach((particle, index) => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.vy += 0.3; // gravity

        ctx.fillStyle = particle.color;
        ctx.fillRect(particle.x, particle.y, particle.size, particle.size);

        if (particle.y > canvas.height + 100) {
          confetti.splice(index, 1);
        }
      });

      if (confetti.length > 0) {
        this.animationFrame = requestAnimationFrame(animate);
      }
    };

    animate();
  }

  private showLoginTest() {
    if (!this.ui) return;
    const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
    if (!popup) return;

    // Render the login prompt in-place for preview/testing
    popup.innerHTML = this.getLoginPromptHTML();

    // Re-bind minimal events used by the login view
    const container = this.ui.uiContainer;
    const signinBtn = container.querySelector('#fillify-signin-btn');
    signinBtn?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openLoginPage' });
      this.hide();
    });

    const skipBtn = container.querySelector('#fillify-skip-login');
    skipBtn?.addEventListener('click', async (e) => {
      e.preventDefault();
      await chrome.runtime.sendMessage({ type: 'setSkipLogin', skip: true });
      this.refreshContent();
    });
  }

  private showDebugBubble() {
    const debugContent = `
      This is a debug bubble for testing purposes.
      The blob animation should be visible behind this text but above the background.
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
      Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula.
    `;
    this.updateReasoningBubble(debugContent);
  }

  private cleanup() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }

    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }

    // 清除按钮重置定时器
    if (this.buttonResetTimer) {
      clearTimeout(this.buttonResetTimer);
      this.buttonResetTimer = null;
    }
  }






}
